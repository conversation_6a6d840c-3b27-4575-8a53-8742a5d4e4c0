package com.mlc.workflow.service.biz;


import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.utils.ProcessParser;
import com.mlc.workflow.dao.entity.MlcAppProcessConfig;
import com.mlc.workflow.service.beans.GetNodeDetailBean;
import com.mlc.workflow.service.entity.MlcAppProcessConfigBizModel;
import com.mlc.workflow.service.biz.handler.NodeDetailHandlerFactory;
import io.nop.api.core.annotations.biz.BizModel;
import io.nop.api.core.annotations.biz.BizQuery;
import io.nop.api.core.annotations.biz.RequestBean;
import io.nop.api.core.annotations.core.Name;
import io.nop.core.context.IServiceContext;
import jakarta.inject.Inject;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/**
 * 流程节点相关接口
 */
@BizModel("WorkflowProcessNode")
public class ProcessFlowNodeBizModel {

    @Inject
    MlcAppProcessConfigBizModel mlcAppProcessConfigBizModel;
    
    @Inject
    NodeDetailHandlerFactory nodeDetailHandlerFactory;

    @BizQuery
    public ProcessNode getProcess(@Name("processId") String processId, IServiceContext context){
        MlcAppProcessConfig mlcAppProcessConfig = mlcAppProcessConfigBizModel.get(processId, true, context);
        String jsonStr = mlcAppProcessConfig.getJsonStr();
        return new ProcessParser().parseProcessNode(jsonStr);
    }


    @BizQuery
    public Map<String, Object> getNodeDetail(@RequestBean GetNodeDetailBean getNodeDetailBean, IServiceContext context){
        // 参数校验
        if (getNodeDetailBean == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        
        if (StringUtils.isEmpty(getNodeDetailBean.getFlowNodeType())) {
            throw new IllegalArgumentException("节点类型不能为空");
        }
        
        // 根据节点类型获取对应的处理器
        return nodeDetailHandlerFactory.getHandler(getNodeDetailBean.getFlowNodeType())
                                       .getNodeDetail(getNodeDetailBean, context);
    }

}
