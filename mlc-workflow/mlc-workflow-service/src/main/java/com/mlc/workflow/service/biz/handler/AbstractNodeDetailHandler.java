package com.mlc.workflow.service.biz.handler;

import com.mlc.workflow.service.beans.GetNodeDetailBean;
import io.nop.core.context.IServiceContext;
import java.util.Map;

/**
 * 节点详情处理器抽象基类
 * 提供通用实现和辅助方法
 */
public abstract class AbstractNodeDetailHandler implements NodeDetailHandler {

    /**
     * 获取当前处理器支持的节点类型
     * 
     * @return 节点类型
     */
    protected abstract String getSupportedNodeType();

    @Override
    public boolean supports(String nodeType) {
        return getSupportedNodeType().equals(nodeType);
    }

    @Override
    public Map<String, Object> getNodeDetail(GetNodeDetailBean request, IServiceContext context) {
        // 查询基础数据
        String basicResult = this.queryNodeData(request, context);

        // 查询额外数据
        Map<String, Object> addResult = this.queryAdditionalData(request, context);

        // 执行后置处理
        return this.postProcess(addResult, request, context);
    }
    
    /**
     * 前置处理逻辑
     * 
     * @param request 请求参数
     * @param context 服务上下文
     */
    protected String queryNodeData(GetNodeDetailBean request, IServiceContext context) {
        // 默认空实现，子类可根据需要覆盖
        return null;
    }
    
    /**
     * 执行具体的节点详情获取逻辑
     * 
     * @param request 请求参数
     * @param context 服务上下文
     * @return 节点详情数据
     */
    protected abstract Map<String, Object> queryAdditionalData(GetNodeDetailBean request, IServiceContext context);
    
    /**
     * 后置处理逻辑
     * 
     * @param result 处理结果
     * @param request 请求参数
     * @param context 服务上下文
     * @return 处理后的结果
     */
    protected Map<String, Object> postProcess(Map<String, Object> result, GetNodeDetailBean request, IServiceContext context) {
        // 默认直接返回结果，子类可根据需要覆盖
        return result;
    }

    /**
     * 处理 controls 字段的辅助方法
     *
     * @param controlsJson controls 字段的 JSON 字符串
     * @return 处理后的 controls 字段
     */
    protected String processControls(String controlsJson) {
        // 这里可以添加对 controls 字段的处理逻辑
        // 例如，解析 JSON、验证字段等
        return controlsJson;
    }

}
