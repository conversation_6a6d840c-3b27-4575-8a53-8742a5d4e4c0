# 一、目标与基础模型

## 1.1 节点分类与语义（结合现有文件）

* **开始节点**：`typeId=0`；有唯一后继。
* **普通节点**：如消息/抄送/审批等（示例：`typeId=27/5/4/...`）。
* **网关节点**：`typeId=1`；有 `gatewayType`：

  * `1`=并行分支（Parallel）
  * `2`=唯一分支（排他/互斥，Exclusive）
    分支由其子“条件/分支头”节点（通常 `typeId=2`）列表承载，并各自形成一条链。
* **分支头(条件)节点**：实现 `IHasConditions`；其后的链称为“分支”。分支尾部可能 `nextId=""` 表示等待汇合。
* **子流程**：节点实现 `IHasSubProcess`；子流程内部也有自己的开始/结束语义。
* **结束哨兵**：不是真节点。`nextId=99` 代表**连接至结束**（作用域内唯一）。主流程与子流程各自允许一个 99，但各自作用域内只能存在一个。

> 在 `workFlow.txt` 中可见示例：
>
> * 网关节点 `65e9874f57c8d64b906944b4`，`gatewayType=2`（唯一分支/排他），其 `flowIds` 指向两个分支头 `65e9874f57c8d64b906944b5` 与 `...b6`；其中一条分支尾部 `nextId=""`，另一条继续到普通节点或结束。也可见某些节点 `nextId="99"` 直达结束哨兵。

## 1.2 关键接口契约

* **IRoutable**：对外暴露 `getNextId()`/`setNextId()`；用于串行链路连线。
* **IHasBranches**（网关）：提供 `getBranchIds()`/`setBranchIds()`、分支顺序维护。
* **IHasConditions**（分支头/条件）：承载条件表达式/路由谓词。
* **IHasSubProcess**：暴露子流程 `ProcessNode`，其内部作用域单独约束 `nextId=99` 唯一性。
* **ProcessNode**：具备 `startEventId` 与 `flowNodeMap`（id→节点）等字典结构；允许按 id O(1) 取节点。

# 二、总体架构与设计模式

## 2.1 组合模式（Composite）

* **流程**→**节点**→**网关**→**分支(条件头+链)**：将网关与分支作为组合结构，统一抽象成“可遍历子结构”。
* 好处：统一 CRUD 与遍历框架，网关/分支/普通节点都可通过相同的访问器（Visitor）进行修改。

## 2.2 策略模式（Strategy）

* **RoutingStrategy**：

  * `ParallelRouting`（并行）：所有分支尾都必须汇合（或都走向 `""` 再由汇合节点继续）。
  * `ExclusiveRouting`（唯一/排他）：分支条件互斥且覆盖；尾部可以 `""` 等待汇合或指向后继。
* **AutoWireStrategy**（上下文自动连线策略）：

  * 针对“插入/删除/替换/断开/连接”的不同场景，提供装配策略（见第 4 章）。

## 2.3 访问者 + 迭代器（Visitor + Iterator）

* **FlowWalker**：可配置 DFS/层序遍历（见 3.3），支持进入子流程。
* **FlowVisitor**：在遍历节点/进入离开分支/进入离开子流程时触发 Hook，用于校验、重连线与副作用处理。

## 2.4 命令模式（Command，无历史）

* 为每个 CRUD 操作定义命令：`AddGatewayCommand`、`DeleteGatewayCommand`、`ChangeGatewayTypeCommand`、`AddBranchCommand`、`...` 等。
* **注意**：你无需实现撤销/重做；命令只负责**前置校验→执行→后置校验与修复**，并确保原子性（抛异常即回滚事务）。

## 2.5 仓储 + 单元工作（Repository + UnitOfWork）

* `ProcessRepository`：对 `ProcessNode` 的持久化与版本号管理。
* `UnitOfWork`：封装一次操作期内的节点加载/锁定、批量提交。

# 三、遍历与结构稳定性

## 3.1 FlowWalker

* 入参：`startId`、深度上限、**访问计数上限**、是否进入子流程。
* **环路保护**：维护 `visitCount[id]++`，超过阈值（默认 2～3 次）视为环嫌疑，触发 `onCycleDetected(id)` Hook（记录告警，不中断 CRUD，但限制连线“向前插入”导致的回环）。

## 3.2 遍历单元

* **串行边**：`IRoutable.nextId → node`
* **网关边**：`IHasBranches.branchIds → branchHead`；从各分支尾继续：

  * 尾 `nextId=""`：意味着“在网关语义中等待汇合”。
  * 尾 `nextId="99"`：即在**当前作用域**结束。
  * 尾 `nextId=<id>`：继续常规连线或汇合。
* **子流程**：进入其 `ProcessNode.startEventId` 再回到宿主节点的后继。

## 3.3 汇合识别（非强制建模汇合节点）

* 并行/排他两类网关均允许“隐式汇合”：即**多个分支尾部 `nextId=""`**，由\*\*网关后的“外部后继”\*\*统一继续（或单独到 99）。
* 若网关自身有 `IRoutable.nextId`，则当所有需要汇合的分支完成后，流程继续到该 nextId。

# 四、通用“上下文自动连线”策略（贯穿 CRUD）

> 统一封装为 `AutoWire` 工具类，并作为所有命令的依赖。

**核心概念**

* **上文节点(Upstream)**：指向当前操作目标的入口边集合（可能>1，比如前驱网关的多分支）。
* **下文节点(Downstream)**：当前目标最后一个实际链尾的下一跳。
* **尾节点(Tail of Segment)**：一段被插入/替换结构的最末端（可能是分支尾汇合点、也可能是普通链末）。

**策略表**

1. **插入 A 介于 X→Y**

  * 断开：`X.next = Y`
  * 连接：`X.next = A.head`
  * 替换下文：`AutoWire.findTail(A).next = Y`（若 `Y==END` 则 `tail.next=99`；需校验本作用域无第二个 99）
2. **删除 A（单链）**

  * 连接：将所有上文 `Ui.next = A.next`；若 `A.next=""` 则 `Ui.next=""`（处于网关内时语义保持）。
3. **替换 A→B（等价于删除+插入）**

  * 套用 (1)(2) 的组合。
4. **网关内部“分支尾”**

  * 若尾 `nextId=""`：**保持为空**，由外层汇合时统一处理。
  * 若尾指向具体 id 或 99：按其语义继续，不强行改写。
5. **跨作用域 END(99) 唯一性**

  * `ScopeResolver`：判定当前操作所属作用域（主流程/某子流程）。
  * `EndGuard.ensureSingleEnd(scope)`：若将产生第二个 99，**禁止**或将原有 99 转为显式终止节点（业务自定其是否允许创建“结束任务”来替代）以满足“每作用域最多一个 99”。

# 五、网关操作（3.1）

## 5.1 新增网关（默认并行分支，满足 3.1.1）

入口：在某个位置 **X→Y** 之间插入网关 **G**。
入参：`placeMode ∈ {LeftShift, NoMove}`。

**通用步骤**

1. 构造网关 `G(gatewayType=1)` 与**两条默认分支** `B1,B2`（各有分支头/条件节点）。
2. `AutoWire.insert(X, G, Y)`：使网关处于 X 与 Y 之间。
3. 依据 `placeMode`：

  * **LeftShift（左侧）**：

    * 将 **Y 以及其以下所有节点**整体搬迁至 **B1** 上（`B1.head.next = Y`，并修正搬迁段上文从 X 改为 B1）。
    * `B1` 和 `B2` 的**分支尾**均设为**直达结束**：`tail.next=99`。
    * **副作用**：需要 `EndGuard` 确认当前作用域尚无 99；若已有 99，则允许 `B1` 保留搬迁链，`B2` 尾设 `""`，网关的外部后继 `G.next=""`，等待汇合（推荐做法以满足单一 99 约束）。
  * **NoMove（不移动）**：

    * 保持 Y 不变，`B1/B2` 的尾部 `nextId=""`。
    * 若网关外部后继存在，需要在**汇合时**由 `AutoWire` 统一连接到 `G.next`。
4. `RoutingStrategy=ParallelRouting`：设置默认并行路由策略。
5. 后置：`Validator.validateStructure()`（见第八章）。

## 5.2 删除网关

* 若**分支数 > 1**：禁止直接删（需先删分支到只剩 1 条，或显式“合并为单链再删”）。
* 若**只剩一条分支**（满足 3.1.2）：

  1. 取唯一分支 `B` 的“有效链”（从分支头之后到分支尾）。
  2. **彻底删除网关与条件分支头**；
  3. 将 `B` 的链作为**普通链段**接入：

    * 所有上文 `Ui.next = B.chainHead`
    * `AutoWire.findTail(B).next = G.next`
  4. 清理遗留引用（从 `flowNodeMap` 移除网关与分支头）。

## 5.3 修改网关类型（并行⇄唯一）

* 从 `ParallelRouting` ⇄ `ExclusiveRouting` 切换：

  * **排他**：检查分支条件**互斥且覆盖**；若否，返回验证错误或自动补“兜底分支”。
  * **并行**：分支条件可忽略；若存在 `nextId=99` 的分支尾且会导致作用域出现双 99，需转化该尾为 `""` 并将 `G.next` 用作汇合后继。
* 全流程：`preCheck → rewriteBranchTailsIfNeeded → setType → validate`.

# 六、分支操作（3.2）

## 6.1 新增分支

* 在网关 `G` 下新增 `B`：

  1. 生成分支头（条件默认空/TRUE）。
  2. 新分支尾 `nextId=""`。
  3. 插入到 `G.branchIds` 的给定位置（默认最后）。
  4. 若 `G` 是排他网关：为新分支追加条件；若覆盖性被破坏，自动在**最后一个分支**设置“else 兜底”。

## 6.2 删除分支

* 删除 `B`：

  1. 若删除后**只剩一条分支**，触发**5.2 网关删除**逻辑（3.1.2 规定）。
  2. 否则：将 `B` 链上所有节点标记删除并从 `flowNodeMap` 清理；不影响其他分支。
  3. 若 `G` 为排他网关，重新检查条件覆盖性。

## 6.3 调整分支顺序

* 仅操作 `G.branchIds` 的顺序。
* 对排他网关：顺序可能影响匹配优先级；若存在“兜底分支”，应固定在最后。

## 6.4 复制分支

* 深拷贝 `B` 链，**为每个节点分配新 id**，并更新链内引用。
* 条件复制时可标记为“未启用”，避免与原分支条件冲突；由调用者在启用前完善条件。
* 校验 99 唯一性：若被复制分支尾是 99，复制时改为 `""`。

# 七、普通节点操作（3.3）

## 7.1 增加普通节点

* 在 `X→Y` 之间插入节点 `N`：`AutoWire.insert(X,N,Y)`。
* 若在**分支尾部**插入，且尾为 `""`：插入后 `N.next=""`。
* 若 `Y==END`（99）：插入后需确保**本作用域无第二个 99**；此时 `N.next=99`。

## 7.2 删除普通节点

* `AutoWire.delete(N)`：将所有上文直接连到下文。
* 若处于**分支尾且 `nextId=""`** 的场景，删除后仍保持 `""`。

## 7.3 修改普通节点

* 更新其业务字段，不动连线；若变更导致“变成子流程/变回普通”，需触发 `ScopeResolver` 重新校验 99 唯一性。

# 八、校验器与不变式

## 8.1 结构不变式

* 每个 `ProcessNode`（主或子流程）内：**最多一个 `nextId=99`** 出边。
* 网关的 `branchIds` 非空；并行≥2，排他≥2（除非进入“只剩一条分支→自动降级删除”流程）。
* 分支尾 `nextId∈{ "", 某节点id, 99 }`。
* 不存在悬挂引用：所有 `nextId` 必须在作用域可解析（或为特殊值 `""/99`）。
* 子流程内的结构独立满足上述约束。

## 8.2 校验流程

* `Validator.validateStructure(process)`：

  * 连通性与悬挂检查（用 `FlowWalker` 从 `startEventId` 出发）。
  * 作用域 99 唯一性检查（主流程 + 所有子流程）。
  * 网关-分支配套检查（条件覆盖/互斥；分支尾一致性）。
  * 循环告警（`visitCount` 超阈值）。

# 九、作用域与 99（第 6 条）实现要点

* `ScopeResolver.of(node)`：返回 {scopeId, isSubProcess, rootProcessId}。
* `EndGuard.ensureSingleEnd(scopeId)`：扫描该 `ProcessNode` 里所有 `nextId==99` 的边数，**≤1**。
* 在**主/子流程各自**可以存在 99，但各自只能 1 个；插入/复制/改变尾指向 99 时，必须调用 `EndGuard`。

# 十一、服务与接口草案

> 以**用例服务**为核心（Application Service），下层调用命令与策略对象。

* `FlowEditService`

  * `addGateway(atEdge: EdgeRef, type: GatewayType=Parallel, placeMode: PlaceMode=NoMove): NodeId`
  * `deleteGateway(gatewayId: NodeId): void`
  * `changeGatewayType(gatewayId: NodeId, type: GatewayType): void`
  * `addBranch(gatewayId: NodeId, index?: number): BranchId`
  * `deleteBranch(gatewayId: NodeId, branchId: BranchId): void`
  * `reorderBranches(gatewayId: NodeId, newOrder: BranchId[]): void`
  * `cloneBranch(gatewayId: NodeId, branchId: BranchId, index?: number): BranchId`
  * `insertNode(atEdge: EdgeRef, newNode: Node): NodeId`
  * `deleteNode(nodeId: NodeId): void`
  * `updateNode(nodeId: NodeId, patch: NodePatch): void`

**说明**

* `EdgeRef = { upstreamId: NodeId, downstreamId: NodeOrEnd }`；若 `downstreamId==END` 表示插入到结束前。
* 所有方法内部统一调用：

  1. `preCheck`（含 `ScopeResolver`/`EndGuard`），
  2. `AutoWire` 执行连线，
  3. `Validator.validateStructure`，
  4. `Repository.save`.

# 十二、关键实现细节与副作用处理清单

* **新增网关 LeftShift**：若目标作用域已有 99，默认把两个默认分支尾都设为 `""`，并将 `G.next` 保留为原 `Y`，等待汇合，确保不引入第二个 99。
* **删除网关（仅剩一分支）**：把分支变成普通链；**彻底删除**分支头与网关节点，避免“空壳节点”残留。
* **复制分支**：新 id、条件关闭、尾指向 `""`，避免 99 冲突。
* **排他网关条件覆盖**：若用户未配置全覆盖条件，系统在最后追加“else”分支（或把最后一条标记为兜底）。
* **遍历环保护**：对“先插入再回指”的误操作能及时给出告警，不阻塞其它 CRUD。
* **子流程**：进入/退出子流程时，分别应用各自的 `EndGuard`；子流程内部的 99 不影响主流程。

# 十三、测试用例清单（最小集）

1. **普通插入/删除**：`X→Y` 插入 N，删除 N；Y=普通/END 两种。
2. **新增并行网关（NoMove）**：两默认分支尾为 `""`，外部后继继续到原 Y。
3. **新增并行网关（LeftShift）**：搬迁 Y 以下到左分支；若已有 99，检查自动降级为 `""` 尾。
4. **删除分支至只剩一条**：触发网关整体删除与分支链提升。
5. **修改网关类型**：并行→排他，排他→并行；验证条件覆盖与 99 唯一性。
6. **复制分支**：确保新 id、尾为 `""`、无第二个 99。
7. **分支顺序调整**：对排他网关验证优先级与兜底分支位置。
8. **子流程中的 99**：子流程内添加直达结束的节点；主流程亦各自 99 唯一。
9. **循环告警**：构造回环边，遍历器告警但不影响 CRUD。